/**
 * APLIKASI POS (Point of Sale) - Google Apps Script
 * Dibuat untuk mengelola penjualan, stok, dan laporan
 */

// ==================== KONFIGURASI UTAMA ====================

// ID Spreadsheet untuk database (ganti dengan ID spreadsheet Anda)
const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID_HERE';

// Nama sheet untuk setiap data
const SHEETS = {
  PRODUCTS: 'Produk',
  TRANSACTIONS: 'Transaksi',
  CUSTOMERS: 'Pelanggan',
  SETTINGS: 'Pengaturan'
};

// ==================== FUNGSI UTAMA ====================

/**
 * Fungsi untuk membuat web app POS
 */
function doGet(e) {
  const page = e.parameter.page || 'dashboard';

  switch(page) {
    case 'dashboard':
      return HtmlService.createTemplateFromFile('dashboard')
        .evaluate()
        .setTitle('POS Dashboard')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'products':
      return HtmlService.createTemplateFromFile('products')
        .evaluate()
        .setTitle('Manajemen Produk')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'reports':
      return HtmlService.createTemplateFromFile('reports')
        .evaluate()
        .setTitle('Laporan Penjualan')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    default:
      return HtmlService.createTemplateFromFile('dashboard')
        .evaluate()
        .setTitle('POS Dashboard')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
  }
}

/**
 * Fungsi untuk include file HTML/CSS/JS
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

// ==================== MANAJEMEN DATABASE ====================

/**
 * Inisialisasi database (sheets)
 */
function initializeDatabase() {
  try {
    let ss = SpreadsheetApp.openById(SPREADSHEET_ID);

    // Buat sheet Produk jika belum ada
    if (!ss.getSheetByName(SHEETS.PRODUCTS)) {
      const productSheet = ss.insertSheet(SHEETS.PRODUCTS);
      productSheet.getRange(1, 1, 1, 7).setValues([
        ['ID', 'Nama Produk', 'Kategori', 'Harga', 'Stok', 'Barcode', 'Tanggal Dibuat']
      ]);
      productSheet.getRange(1, 1, 1, 7).setFontWeight('bold');
    }

    // Buat sheet Transaksi jika belum ada
    if (!ss.getSheetByName(SHEETS.TRANSACTIONS)) {
      const transactionSheet = ss.insertSheet(SHEETS.TRANSACTIONS);
      transactionSheet.getRange(1, 1, 1, 8).setValues([
        ['ID Transaksi', 'Tanggal', 'Total', 'Metode Bayar', 'Kasir', 'Items', 'Diskon', 'Status']
      ]);
      transactionSheet.getRange(1, 1, 1, 8).setFontWeight('bold');
    }

    // Buat sheet Pelanggan jika belum ada
    if (!ss.getSheetByName(SHEETS.CUSTOMERS)) {
      const customerSheet = ss.insertSheet(SHEETS.CUSTOMERS);
      customerSheet.getRange(1, 1, 1, 5).setValues([
        ['ID', 'Nama', 'Email', 'Telepon', 'Alamat']
      ]);
      customerSheet.getRange(1, 1, 1, 5).setFontWeight('bold');
    }

    // Buat sheet Pengaturan jika belum ada
    if (!ss.getSheetByName(SHEETS.SETTINGS)) {
      const settingsSheet = ss.insertSheet(SHEETS.SETTINGS);
      settingsSheet.getRange(1, 1, 5, 2).setValues([
        ['Setting', 'Value'],
        ['Nama Toko', 'Toko Saya'],
        ['Alamat Toko', 'Jl. Contoh No. 123'],
        ['Telepon Toko', '021-12345678'],
        ['Pajak (%)', '10']
      ]);
      settingsSheet.getRange(1, 1, 1, 2).setFontWeight('bold');
    }

    return { success: true, message: 'Database berhasil diinisialisasi' };
  } catch (error) {
    return { success: false, message: 'Error: ' + error.toString() };
  }
}

/**
 * Mendapatkan referensi spreadsheet
 */
function getSpreadsheet() {
  try {
    return SpreadsheetApp.openById(SPREADSHEET_ID);
  } catch (error) {
    throw new Error('Tidak dapat mengakses spreadsheet. Pastikan SPREADSHEET_ID sudah benar.');
  }
}

// ==================== MANAJEMEN PRODUK ====================

/**
 * Mendapatkan semua produk
 */
function getAllProducts() {
  try {
    const ss = getSpreadsheet();
    const sheet = ss.getSheetByName(SHEETS.PRODUCTS);
    const data = sheet.getDataRange().getValues();

    if (data.length <= 1) return [];

    const products = [];
    for (let i = 1; i < data.length; i++) {
      products.push({
        id: data[i][0],
        name: data[i][1],
        category: data[i][2],
        price: data[i][3],
        stock: data[i][4],
        barcode: data[i][5],
        createdDate: data[i][6]
      });
    }

    return products;
  } catch (error) {
    throw new Error('Error mengambil data produk: ' + error.toString());
  }
}

/**
 * Menambah produk baru
 */
function addProduct(productData) {
  try {
    const ss = getSpreadsheet();
    const sheet = ss.getSheetByName(SHEETS.PRODUCTS);

    // Generate ID unik
    const id = 'PRD' + Date.now();
    const currentDate = new Date();

    const newRow = [
      id,
      productData.name,
      productData.category,
      productData.price,
      productData.stock,
      productData.barcode || '',
      currentDate
    ];

    sheet.appendRow(newRow);

    return {
      success: true,
      message: 'Produk berhasil ditambahkan',
      productId: id
    };
  } catch (error) {
    return {
      success: false,
      message: 'Error menambah produk: ' + error.toString()
    };
  }
}